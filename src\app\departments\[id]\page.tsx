"use client";

import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/dashboard-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building2,
  Users,
  DollarSign,
  Target,
  TrendingUp,
} from "lucide-react";

// Mock department data
const department = {
  id: 1,
  name: "Engineering",
  description: "Software development and technical operations",
  manager: "<PERSON>",
  managerEmail: "<EMAIL>",
  employeeCount: 45,
  targetHeadcount: 50,
  budget: "$2,500,000",
  status: "Active",
  location: "San Francisco, CA",
  email: "<EMAIL>",
  phone: "+****************",
  slackChannel: "#engineering",
  establishedDate: "2020-01-15",
  costCenter: "CC-001",
};

const employees = [
  { name: "John Doe", position: "Senior Developer", avatar: "/avatars/01.png" },
  {
    name: "Jane Smith",
    position: "Frontend Developer",
    avatar: "/avatars/02.png",
  },
  {
    name: "Mike Johnson",
    position: "Backend Developer",
    avatar: "/avatars/03.png",
  },
  {
    name: "Emily Brown",
    position: "DevOps Engineer",
    avatar: "/avatars/04.png",
  },
  { name: "David Wilson", position: "QA Engineer", avatar: "/avatars/05.png" },
];

const metrics = [
  { name: "Team Productivity", value: 87, target: 90 },
  { name: "Project Completion Rate", value: 94, target: 95 },
  { name: "Employee Satisfaction", value: 4.2, target: 4.5, isRating: true },
  { name: "Budget Utilization", value: 78, target: 85 },
];

export default function DepartmentDetailPage() {
  const router = useRouter();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Active
          </Badge>
        );
      case "Inactive":
        return <Badge variant="destructive">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const headcountProgress =
    (department.employeeCount / department.targetHeadcount) * 100;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">
              Department Details
            </h1>
            <p className="text-muted-foreground">
              View and manage department information
            </p>
          </div>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Edit Department
          </Button>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-4">
              <div className="flex h-20 w-20 items-center justify-center rounded-lg bg-primary/10">
                <Building2 className="h-10 w-10 text-primary" />
              </div>
              <div className="flex-1 space-y-2">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">{department.name}</h2>
                  {getStatusBadge(department.status)}
                </div>
                <p className="text-lg text-muted-foreground">
                  {department.description}
                </p>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>{department.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{department.employeeCount} employees</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Est. {department.establishedDate}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Current Headcount
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {department.employeeCount}
              </div>
              <div className="mt-2">
                <Progress value={headcountProgress} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {department.employeeCount} of {department.targetHeadcount}{" "}
                  target
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Annual Budget
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{department.budget}</div>
              <p className="text-xs text-muted-foreground">78% utilized</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cost Center</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{department.costCenter}</div>
              <p className="text-xs text-muted-foreground">
                Active cost center
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">87%</div>
              <p className="text-xs text-muted-foreground">
                Overall performance
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="employees">Employees</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{department.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{department.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">💬</span>
                    <span>{department.slackChannel}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Department Manager</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarFallback>
                        {department.manager
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{department.manager}</p>
                      <p className="text-sm text-muted-foreground">
                        {department.managerEmail}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Department Manager
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="employees" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Team Members</CardTitle>
                <CardDescription>
                  Current employees in the {department.name} department
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {employees.map((employee, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-3 p-3 border rounded-lg"
                    >
                      <Avatar className="h-10 w-10">
                        <AvatarImage
                          src={employee.avatar}
                          alt={employee.name}
                        />
                        <AvatarFallback>
                          {employee.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{employee.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {employee.position}
                        </p>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full">
                    View All Employees ({department.employeeCount})
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>
                  Key performance indicators for the department
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {metrics.map((metric, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{metric.name}</span>
                        <span>
                          {metric.isRating
                            ? `${metric.value}/5`
                            : `${metric.value}%`}
                        </span>
                      </div>
                      <Progress
                        value={
                          metric.isRating
                            ? (metric.value / 5) * 100
                            : metric.value
                        }
                        className="h-2"
                      />
                      <p className="text-xs text-muted-foreground">
                        Target:{" "}
                        {metric.isRating
                          ? `${metric.target}/5`
                          : `${metric.target}%`}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
