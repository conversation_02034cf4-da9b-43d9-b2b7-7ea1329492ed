"use client";

import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/dashboard-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building2,
  User,
} from "lucide-react";

// Mock employee data
const employee = {
  id: 1,
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  department: "Engineering",
  position: "Senior Developer",
  status: "Active",
  joinDate: "2023-01-15",
  employeeId: "EMP001",
  manager: "<PERSON>",
  salary: "$85,000",
  employmentType: "Full Time",
  address: "123 Main St, San Francisco, CA 94105",
  dateOfBirth: "1990-05-15",
  avatar: "/avatars/01.png",
};

const leaveHistory = [
  {
    type: "Annual Leave",
    dates: "Dec 20-24, 2023",
    status: "Approved",
    days: 5,
  },
  { type: "Sick Leave", dates: "Nov 15, 2023", status: "Approved", days: 1 },
  {
    type: "Personal Leave",
    dates: "Oct 10-11, 2023",
    status: "Approved",
    days: 2,
  },
];

const performanceReviews = [
  {
    period: "Q4 2023",
    rating: "Excellent",
    score: "4.8/5",
    reviewer: "Sarah Johnson",
  },
  {
    period: "Q3 2023",
    rating: "Good",
    score: "4.2/5",
    reviewer: "Sarah Johnson",
  },
  {
    period: "Q2 2023",
    rating: "Excellent",
    score: "4.7/5",
    reviewer: "Sarah Johnson",
  },
];

export default function EmployeeDetailPage() {
  const router = useRouter();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Active
          </Badge>
        );
      case "On Leave":
        return <Badge variant="secondary">On Leave</Badge>;
      case "Inactive":
        return <Badge variant="destructive">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">
              Employee Details
            </h1>
            <p className="text-muted-foreground">
              View and manage employee information
            </p>
          </div>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Edit Employee
          </Button>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={employee.avatar} alt={employee.name} />
                <AvatarFallback className="text-lg">
                  {employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-2">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">{employee.name}</h2>
                  {getStatusBadge(employee.status)}
                </div>
                <p className="text-lg text-muted-foreground">
                  {employee.position}
                </p>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Building2 className="h-4 w-4" />
                    <span>{employee.department}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>ID: {employee.employeeId}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {employee.joinDate}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="leave">Leave History</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{employee.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{employee.phone}</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <span>{employee.address}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Employment Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Manager
                      </p>
                      <p>{employee.manager}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Employment Type
                      </p>
                      <p>{employee.employmentType}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Salary
                      </p>
                      <p>{employee.salary}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Date of Birth
                      </p>
                      <p>{employee.dateOfBirth}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="leave" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Leave History</CardTitle>
                <CardDescription>
                  Employee&apos;s leave requests and history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {leaveHistory.map((leave, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium">{leave.type}</p>
                        <p className="text-sm text-muted-foreground">
                          {leave.dates}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={
                            leave.status === "Approved"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {leave.status}
                        </Badge>
                        <p className="text-sm text-muted-foreground mt-1">
                          {leave.days} days
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Reviews</CardTitle>
                <CardDescription>
                  Employee&apos;s performance review history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performanceReviews.map((review, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium">{review.period}</p>
                        <p className="text-sm text-muted-foreground">
                          Reviewed by {review.reviewer}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{review.score}</p>
                        <Badge variant="outline">{review.rating}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
