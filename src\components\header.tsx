"use client";

import Link from "next/link";
import { <PERSON>, Search, User, Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,

} from "@/components/ui/dropdown-menu";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ThemeToggle } from "@/components/theme-toggle";
import { cn } from "@/lib/utils";

// Navigation menu structure
const navigationMenus = [
  {
    title: "Danh mục",
    items: [
      {
        title: "Phòng ban",
        items: [
          { title: "Danh sách phòng ban", href: "/departments" },
          { title: "Thêm phòng ban", href: "/departments/add" },
        ],
      },
      {
        title: "Nhân viên",
        items: [
          { title: "Danh sách nhân viên", href: "/employees" },
          { title: "Thêm nhân viên", href: "/employees/add" },
        ],
      },
    ],
  },
  {
    title: "Quản lý",
    items: [
      {
        title: "Nghỉ phép",
        items: [
          { title: "Danh sách đơn nghỉ", href: "/leave" },
          { title: "Đăng ký nghỉ phép", href: "/leave/request" },
        ],
      },
      {
        title: "Lương",
        items: [
          { title: "Bảng lương", href: "/payroll" },
          { title: "Báo cáo lương", href: "/payroll/reports" },
        ],
      },
    ],
  },
  {
    title: "Báo cáo",
    items: [
      {
        title: "Thống kê",
        items: [
          { title: "Báo cáo tổng quan", href: "/reports" },
          { title: "Phân tích nhân sự", href: "/reports/analytics" },
        ],
      },
    ],
  },
];

const ListItem = ({ className, title, children, href, ...props }: any) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          href={href}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  );
};

export function Header() {
  return (
    <header className="border-b bg-background">
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        {/* Left: Logo and Mobile Menu */}
        <div className="flex items-center gap-4">
          {/* Mobile Navigation Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <div className="space-y-4">
                <div className="pb-4">
                  <h2 className="text-lg font-semibold">Menu</h2>
                </div>
                {navigationMenus.map((menu) => (
                  <div key={menu.title} className="space-y-2">
                    <h3 className="font-medium text-sm text-muted-foreground">
                      {menu.title}
                    </h3>
                    {menu.items.map((category) => (
                      <div key={category.title} className="space-y-1 pl-4">
                        <h4 className="font-medium text-sm">
                          {category.title}
                        </h4>
                        <div className="space-y-1 pl-4">
                          {category.items.map((item) => (
                            <Link
                              key={item.title}
                              href={item.href}
                              className="block text-sm text-muted-foreground hover:text-foreground transition-colors"
                            >
                              {item.title}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </SheetContent>
          </Sheet>

          {/* Logo */}
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">
                HR
              </span>
            </div>
            <span className="hidden sm:inline-block font-bold text-lg">
              HRM Dashboard
            </span>
          </Link>
        </div>

        {/* Center: 3-level Navigation */}
        <div className="hidden md:flex">
          <NavigationMenu>
            <NavigationMenuList>
              {navigationMenus.map((menu) => (
                <NavigationMenuItem key={menu.title}>
                  <NavigationMenuTrigger className="h-10">
                    {menu.title}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[600px] gap-3 p-4 md:grid-cols-2">
                      {menu.items.map((category) => (
                        <div key={category.title} className="space-y-2">
                          <h4 className="text-sm font-medium leading-none text-muted-foreground">
                            {category.title}
                          </h4>
                          <ul className="space-y-1">
                            {category.items.map((item) => (
                              <ListItem
                                key={item.title}
                                title={item.title}
                                href={item.href}
                                className="text-sm"
                              />
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        {/* Right: Avatar and actions */}
        <div className="flex items-center gap-2">
          {/* Search - hidden on mobile */}
          <div className="relative hidden lg:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Tìm kiếm..."
              className="w-48 pl-8"
            />
          </div>

          {/* Mobile search button */}
          <Button variant="ghost" size="icon" className="lg:hidden">
            <Search className="h-4 w-4" />
          </Button>

          {/* Notifications */}
          <Button variant="ghost" size="icon">
            <Bell className="h-4 w-4" />
          </Button>

          {/* Theme toggle */}
          <ThemeToggle />

          {/* User avatar dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/avatars/01.png" alt="@user" />
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">John Doe</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    <EMAIL>
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link href="/profile" className="w-full">
                  Hồ sơ cá nhân
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/settings" className="w-full">
                  Cài đặt
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Đăng xuất</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
