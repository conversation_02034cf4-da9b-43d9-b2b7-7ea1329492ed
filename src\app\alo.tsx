// Build a Human Resource Management (HRM) System Dashboard using Next.js + TypeScript + Shadcn UI + next-themes.
//
// 🎯 Context:
// - This is a dashboard for managing a Human Resources system, with menus like:
//   - "<PERSON><PERSON><PERSON>n lý nhân sự", "<PERSON><PERSON><PERSON> công", "<PERSON><PERSON><PERSON> lương", "<PERSON>h mục cư trú", v.v.
// - Design should be professional, admin-panel style.
//
// 🧱 Layout:
// - Header:
//   - Left: Logo (clickable)
//   - Center: 3-level navigation (like WinForm menu):
//     Example:
//       - Danh mục
//         - Phòng ban
//           - Danh sách phòng ban
//   - Right: Avatar (dropdown with profile/logout)
// - Main:
//   - Tab control area (multi-tab layout) shows pages when menu item clicked
// - Footer:
//   - Fixed at bottom with static or dynamic info (e.g., © 2025, current time)
//
// 🧠 Features:
// - Navigation:
//   - Tree-style menu with 3 levels
//   - Clicking a 3rd-level item opens a tab (like TabControl in WinForm)
//   - Tabs:
//     - Closable
//     - Unique (don’t allow duplicate tabs)
//     - Persist the active tab state (client-side)
// - Logo click:
//   - If more than 1 tab open, show confirmation modal
//   - Confirm to close all tabs
//
// 🌗 Theme (Dark/Light Mode):
// - Integrate `next-themes`
// - Add toggle button to switch between light/dark mode
// - Use `class="dark"` strategy
//
// 🧩 Technical Requirements:
// - Use Shadcn UI components
// - Use `next-themes` for dark/light mode
// - Use `Zustand`, `useReducer`, or React Context to manage tab state globally
// - No hydration errors
// - Support App Router (`app/` directory)
//
// 📁 Folder Structure:
// - components/
//   - header/
//   - navigation/
//   - tab/
//   - footer/
//   - theme-toggle.tsx
// - contexts/ or stores/
// - lib/
// - app/
//   - layout.tsx
//   - page.tsx
// - types/
// - styles/
//
// ✅ Output:
// - Full working code for layout, header, tabs, navigation, and theme toggle
// - Include usage of `<ThemeProvider>`
// - Provide sample tab content pages and mock navigation items
// - Ensure TypeScript type safety and correct imports
