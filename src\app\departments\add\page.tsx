"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save } from "lucide-react"

export default function AddDepartmentPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      router.push("/departments")
    }, 1000)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Add New Department</h1>
            <p className="text-muted-foreground">
              Create a new department in your organization
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Department Information</CardTitle>
              <CardDescription>
                Basic information about the department
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Department Name</Label>
                <Input id="name" placeholder="Engineering" required />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  placeholder="Brief description of the department's role and responsibilities"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" placeholder="San Francisco, CA" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">Annual Budget</Label>
                  <Input id="budget" type="number" placeholder="1000000" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Management Details</CardTitle>
              <CardDescription>
                Department leadership and reporting structure
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="manager">Department Manager</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department manager" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sarah.johnson">Sarah Johnson</SelectItem>
                    <SelectItem value="mike.wilson">Mike Wilson</SelectItem>
                    <SelectItem value="emily.davis">Emily Davis</SelectItem>
                    <SelectItem value="david.brown">David Brown</SelectItem>
                    <SelectItem value="lisa.anderson">Lisa Anderson</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="parentDepartment">Parent Department (Optional)</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select parent department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None (Top Level)</SelectItem>
                    <SelectItem value="engineering">Engineering</SelectItem>
                    <SelectItem value="operations">Operations</SelectItem>
                    <SelectItem value="corporate">Corporate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="costCenter">Cost Center</Label>
                  <Input id="costCenter" placeholder="CC-001" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="establishedDate">Established Date</Label>
                  <Input id="establishedDate" type="date" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Goals & Objectives</CardTitle>
              <CardDescription>
                Department goals and key performance indicators
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="objectives">Primary Objectives</Label>
                <Textarea 
                  id="objectives" 
                  placeholder="List the main objectives and goals for this department"
                  rows={4}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="targetHeadcount">Target Headcount</Label>
                  <Input id="targetHeadcount" type="number" placeholder="50" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currentHeadcount">Current Headcount</Label>
                  <Input id="currentHeadcount" type="number" placeholder="0" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>
                Department contact details and communication preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Department Email</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Department Phone</Label>
                  <Input id="phone" type="tel" placeholder="+****************" />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="slackChannel">Slack Channel (Optional)</Label>
                <Input id="slackChannel" placeholder="#engineering" />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="mr-2 h-4 w-4" />
              {isLoading ? "Creating..." : "Create Department"}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}
