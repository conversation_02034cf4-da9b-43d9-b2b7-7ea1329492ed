# HRM Dashboard - Human Resource Management System

A comprehensive Human Resource Management System built with Next.js 15, TypeScript, Tailwind CSS, and Shadcn UI.

## Features

### 🏢 Core Modules

- **Dashboard Overview** - Key metrics, recent activities, and quick actions
- **Employee Management** - Complete CRUD operations for employee data
- **Department Management** - Organize and manage company departments
- **Leave Management** - Handle leave requests, approvals, and calendar view
- **Payroll Management** - Process payroll, generate salary slips, and reports
- **Reports & Analytics** - Comprehensive HR analytics with charts and insights
- **Settings & Configuration** - Company settings, user preferences, and security

### 🎨 Design & UX

- **Modern UI** - Built with Shadcn UI components
- **Dark/Light Theme** - Automatic theme switching with next-themes
- **Responsive Design** - Fully responsive across all devices
- **Mobile Optimized** - Mobile-first approach with touch-friendly interface

### 🔐 Authentication

- **Login/Register** - Secure authentication pages
- **Password Reset** - Forgot password functionality
- **Form Validation** - Client-side validation with proper error handling

## Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn UI
- **Icons**: Lucide React
- **Charts**: Recharts
- **Forms**: React Hook Form + Zod
- **Theme**: next-themes
- **Date Handling**: date-fns
- **Testing**: Jest + React Testing Library

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd hrm-next-client
```

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard page
│   ├── employees/         # Employee management
│   ├── departments/       # Department management
│   ├── leave/            # Leave management
│   ├── payroll/          # Payroll management
│   ├── reports/          # Reports and analytics
│   └── settings/         # Settings pages
├── components/            # Reusable components
│   ├── ui/               # Shadcn UI components
│   ├── dashboard-layout.tsx
│   ├── sidebar.tsx
│   ├── header.tsx
│   └── theme-provider.tsx
├── lib/                  # Utility functions
└── __tests__/           # Test files
```

## Responsive Design

The application is fully responsive with:

- Mobile-first approach
- Collapsible sidebar for mobile
- Responsive tables and cards
- Touch-friendly interface
- Optimized for tablets and mobile devices

## Testing

The project includes Jest and React Testing Library for testing:

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```
