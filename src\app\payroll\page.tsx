"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  MoreHorizontal,
  Download,
  Eye,
  DollarSign,
  Calculator,
  FileText,
  TrendingUp,
  Users,
  Calendar,
} from "lucide-react";

// Mock payroll data
const payrollRecords = [
  {
    id: 1,
    employee: "John Doe",
    employeeId: "EMP001",
    avatar: "/avatars/01.png",
    department: "Engineering",
    baseSalary: 85000,
    grossPay: 7083.33,
    deductions: 1416.67,
    netPay: 5666.66,
    status: "Processed",
    payPeriod: "December 2023",
  },
  {
    id: 2,
    employee: "Sarah Johnson",
    employeeId: "EMP002",
    avatar: "/avatars/02.png",
    department: "Marketing",
    baseSalary: 75000,
    grossPay: 6250.0,
    deductions: 1250.0,
    netPay: 5000.0,
    status: "Processed",
    payPeriod: "December 2023",
  },
  {
    id: 3,
    employee: "Mike Wilson",
    employeeId: "EMP003",
    avatar: "/avatars/03.png",
    department: "Sales",
    baseSalary: 65000,
    grossPay: 5416.67,
    deductions: 1083.33,
    netPay: 4333.34,
    status: "Pending",
    payPeriod: "January 2024",
  },
  {
    id: 4,
    employee: "Emily Davis",
    employeeId: "EMP004",
    avatar: "/avatars/04.png",
    department: "HR",
    baseSalary: 70000,
    grossPay: 5833.33,
    deductions: 1166.67,
    netPay: 4666.66,
    status: "Processed",
    payPeriod: "December 2023",
  },
];

const payrollSummary = {
  totalEmployees: 245,
  totalGrossPay: 1250000,
  totalDeductions: 250000,
  totalNetPay: 1000000,
  averageSalary: 75000,
  payrollBudget: 18000000,
};

export default function PayrollPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("december-2023");

  const filteredRecords = payrollRecords.filter(
    (record) =>
      record.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Processed":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Processed
          </Badge>
        );
      case "Pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "Failed":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Payroll Management
            </h1>
            <p className="text-muted-foreground">
              Manage employee payroll and salary processing
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button>
              <Calculator className="mr-2 h-4 w-4" />
              Process Payroll
            </Button>
          </div>
        </div>

        {/* Payroll Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Employees
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {payrollSummary.totalEmployees}
              </div>
              <p className="text-xs text-muted-foreground">Active employees</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Monthly Gross Pay
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(payrollSummary.totalGrossPay)}
              </div>
              <p className="text-xs text-muted-foreground">
                +5% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Deductions
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(payrollSummary.totalDeductions)}
              </div>
              <p className="text-xs text-muted-foreground">20% of gross pay</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Payroll</CardTitle>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(payrollSummary.totalNetPay)}
              </div>
              <p className="text-xs text-muted-foreground">
                Ready for processing
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="current-payroll" className="space-y-4">
          <TabsList>
            <TabsTrigger value="current-payroll">Current Payroll</TabsTrigger>
            <TabsTrigger value="salary-slips">Salary Slips</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="current-payroll" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Payroll Records</CardTitle>
                <CardDescription>
                  Employee payroll information for the selected period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 mb-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <Select
                    value={selectedPeriod}
                    onValueChange={setSelectedPeriod}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Select pay period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="january-2024">January 2024</SelectItem>
                      <SelectItem value="december-2023">
                        December 2023
                      </SelectItem>
                      <SelectItem value="november-2023">
                        November 2023
                      </SelectItem>
                      <SelectItem value="october-2023">October 2023</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Gross Pay</TableHead>
                      <TableHead>Deductions</TableHead>
                      <TableHead>Net Pay</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={record.avatar}
                                alt={record.employee}
                              />
                              <AvatarFallback>
                                {record.employee
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {record.employee}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {record.employeeId}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{record.department}</TableCell>
                        <TableCell>{formatCurrency(record.grossPay)}</TableCell>
                        <TableCell>
                          {formatCurrency(record.deductions)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(record.netPay)}
                        </TableCell>
                        <TableCell>{getStatusBadge(record.status)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <FileText className="mr-2 h-4 w-4" />
                                Generate Slip
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Download PDF
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="salary-slips" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Salary Slips</CardTitle>
                <CardDescription>
                  Generate and manage employee salary slips
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-64 text-muted-foreground">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto mb-4" />
                    <p>Salary slip generation interface</p>
                    <p className="text-sm">
                      Individual and bulk slip generation
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Payroll Reports</CardTitle>
                <CardDescription>
                  Generate various payroll reports and analytics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <Button variant="outline" className="h-24 flex-col">
                    <FileText className="h-6 w-6 mb-2" />
                    <span>Monthly Report</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex-col">
                    <TrendingUp className="h-6 w-6 mb-2" />
                    <span>Tax Report</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex-col">
                    <DollarSign className="h-6 w-6 mb-2" />
                    <span>Deductions Report</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex-col">
                    <Users className="h-6 w-6 mb-2" />
                    <span>Department Wise</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex-col">
                    <Calendar className="h-6 w-6 mb-2" />
                    <span>Annual Summary</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex-col">
                    <Calculator className="h-6 w-6 mb-2" />
                    <span>Custom Report</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
